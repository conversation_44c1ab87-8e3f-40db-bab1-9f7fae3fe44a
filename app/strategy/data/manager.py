"""
数据管理器

负责数据的读取、查询和管理
"""
import os
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from pathlib import Path

import pandas as pd
import duckdb

from ..config import settings

logger = logging.getLogger(__name__)

class DataManager:
    """数据管理器"""
    
    def __init__(self):
        self.config = settings.data
        self.db_path = self.config.database_path
        self._ensure_database()
    
    def _ensure_database(self):
        """确保数据库存在并初始化表结构"""
        try:
            # 确保数据库目录存在
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            # 连接数据库并创建表
            with duckdb.connect(self.db_path) as conn:
                self._create_tables(conn)
                
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _create_tables(self, conn: duckdb.DuckDBPyConnection):
        """创建数据库表"""
        # 创建股票基础信息表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS stock_info (
                symbol VARCHAR PRIMARY KEY,
                name VARCHAR,
                market VARCHAR,
                industry VARCHAR,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建股票日线数据表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS stock_daily (
                symbol VARCHAR,
                date DATE,
                open DECIMAL(10,3),
                high DECIMAL(10,3),
                low DECIMAL(10,3),
                close DECIMAL(10,3),
                volume BIGINT,
                amount DECIMAL(15,2),
                pct_change DECIMAL(8,3),
                change DECIMAL(8,3),
                amplitude DECIMAL(8,3),
                turnover DECIMAL(8,3),
                avg_price DECIMAL(10,3),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (symbol, date)
            )
        """)
        
        # 创建技术指标表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS stock_indicators (
                symbol VARCHAR,
                date DATE,
                indicator_name VARCHAR,
                indicator_value DECIMAL(15,6),
                indicator_data JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (symbol, date, indicator_name)
            )
        """)
        
        # 创建策略信号表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS strategy_signals (
                id INTEGER PRIMARY KEY,
                symbol VARCHAR,
                date DATE,
                strategy_name VARCHAR,
                signal_type VARCHAR, -- 'BUY', 'SELL', 'HOLD'
                signal_strength DECIMAL(5,3), -- 信号强度 0-1
                signal_data JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建形态识别表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS pattern_recognition (
                id INTEGER PRIMARY KEY,
                symbol VARCHAR,
                date DATE,
                pattern_name VARCHAR,
                pattern_type VARCHAR, -- 'BULLISH', 'BEARISH', 'NEUTRAL'
                confidence DECIMAL(5,3), -- 置信度 0-1
                pattern_data JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        logger.info("数据库表结构初始化完成")
    
    def load_stock_data(self, symbol: str, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """加载股票数据

        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            股票数据DataFrame
        """
        # 首先尝试从日期文件加载
        daily_files_data = self.load_stock_data_from_daily_files(symbol, start_date, end_date)

        if not daily_files_data.empty:
            return daily_files_data

        # 如果日期文件没有数据，尝试从按股票组织的parquet文件加载
        parquet_data = self._load_from_parquet(symbol, start_date, end_date)

        if not parquet_data.empty:
            return parquet_data

        # 如果parquet文件没有数据，从数据库加载
        return self._load_from_database(symbol, start_date, end_date)
    
    def _load_from_parquet(self, symbol: str, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """从parquet文件加载数据"""
        try:
            data_frames = []
            raw_path = Path(self.config.raw_data_path)
            
            if not raw_path.exists():
                return pd.DataFrame()
            
            # 确定日期范围
            if start_date:
                start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            else:
                start_dt = datetime(2023, 1, 1)
            
            if end_date:
                end_dt = datetime.strptime(end_date, "%Y-%m-%d")
            else:
                end_dt = datetime.now()
            
            # 遍历年月目录
            current_dt = start_dt.replace(day=1)
            while current_dt <= end_dt:
                year = current_dt.year
                month = current_dt.month
                
                file_path = raw_path / str(year) / f"{month:02d}" / f"{symbol}.parquet"
                
                if file_path.exists():
                    try:
                        df = pd.read_parquet(file_path)
                        if not df.empty:
                            data_frames.append(df)
                    except Exception as e:
                        logger.warning(f"读取文件 {file_path} 失败: {e}")
                
                # 移动到下个月
                if month == 12:
                    current_dt = current_dt.replace(year=year+1, month=1)
                else:
                    current_dt = current_dt.replace(month=month+1)
            
            if not data_frames:
                return pd.DataFrame()
            
            # 合并数据
            combined_data = pd.concat(data_frames, ignore_index=True)
            
            # 确保日期列是datetime类型
            if 'date' in combined_data.columns:
                combined_data['date'] = pd.to_datetime(combined_data['date'])
            
            # 按日期过滤
            if start_date:
                combined_data = combined_data[combined_data['date'] >= start_date]
            if end_date:
                combined_data = combined_data[combined_data['date'] <= end_date]
            
            # 按日期排序
            combined_data = combined_data.sort_values('date').reset_index(drop=True)
            
            logger.debug(f"从parquet文件加载股票 {symbol} 数据 {len(combined_data)} 条")
            return combined_data
            
        except Exception as e:
            logger.error(f"从parquet文件加载股票 {symbol} 数据失败: {e}")
            return pd.DataFrame()
    
    def _load_from_database(self, symbol: str, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """从数据库加载数据"""
        try:
            with duckdb.connect(self.db_path) as conn:
                query = "SELECT * FROM stock_daily WHERE symbol = ?"
                params = [symbol]
                
                if start_date:
                    query += " AND date >= ?"
                    params.append(start_date)
                
                if end_date:
                    query += " AND date <= ?"
                    params.append(end_date)
                
                query += " ORDER BY date"
                
                data = conn.execute(query, params).df()
                
                logger.debug(f"从数据库加载股票 {symbol} 数据 {len(data)} 条")
                return data
                
        except Exception as e:
            logger.error(f"从数据库加载股票 {symbol} 数据失败: {e}")
            return pd.DataFrame()
    
    def save_stock_data_to_db(self, data: pd.DataFrame) -> bool:
        """保存股票数据到数据库
        
        Args:
            data: 股票数据
            
        Returns:
            是否成功
        """
        if data.empty:
            return False
        
        try:
            with duckdb.connect(self.db_path) as conn:
                # 使用INSERT OR REPLACE避免重复数据
                conn.execute("""
                    INSERT OR REPLACE INTO stock_daily 
                    SELECT * FROM data
                """)
                
                logger.info(f"保存 {len(data)} 条股票数据到数据库")
                return True
                
        except Exception as e:
            logger.error(f"保存股票数据到数据库失败: {e}")
            return False
    
    def get_available_symbols(self) -> List[str]:
        """获取可用的股票代码列表"""
        symbols = set()
        
        # 从parquet文件获取
        raw_path = Path(self.config.raw_data_path)
        if raw_path.exists():
            for year_dir in raw_path.iterdir():
                if not year_dir.is_dir():
                    continue
                for month_dir in year_dir.iterdir():
                    if not month_dir.is_dir():
                        continue
                    for file_path in month_dir.glob("*.parquet"):
                        symbols.add(file_path.stem)
        
        # 从数据库获取
        try:
            with duckdb.connect(self.db_path) as conn:
                db_symbols = conn.execute("SELECT DISTINCT symbol FROM stock_daily").fetchall()
                symbols.update([s[0] for s in db_symbols])
        except Exception as e:
            logger.warning(f"从数据库获取股票代码失败: {e}")
        
        return sorted(list(symbols))

    def load_daily_data(self, date: str) -> pd.DataFrame:
        """加载指定日期的所有股票数据

        Args:
            date: 日期字符串 (YYYY-MM-DD)

        Returns:
            该日期的所有股票数据
        """
        try:
            # 解析日期
            date_obj = datetime.strptime(date, '%Y-%m-%d')
            year = date_obj.year
            month = date_obj.month

            # 构建文件路径
            daily_data_path = Path(settings.data.raw_data_path) / "daily"
            file_path = daily_data_path / str(year) / f"{month:02d}" / f"{date}.parquet"

            if not file_path.exists():
                logger.warning(f"日期文件不存在: {file_path}")
                return pd.DataFrame()

            # 读取数据
            data = pd.read_parquet(file_path)

            # 确保日期列是datetime类型
            if 'date' in data.columns:
                data['date'] = pd.to_datetime(data['date'])

            logger.info(f"加载日期 {date} 数据成功: {len(data)} 条记录, {data['symbol'].nunique()} 只股票")
            return data

        except Exception as e:
            logger.error(f"加载日期 {date} 数据失败: {e}")
            return pd.DataFrame()

    def load_date_range_data(self, start_date: str, end_date: str,
                           symbols: List[str] = None) -> pd.DataFrame:
        """加载日期范围内的股票数据

        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            symbols: 股票代码列表，None表示所有股票

        Returns:
            日期范围内的股票数据
        """
        try:
            start = datetime.strptime(start_date, '%Y-%m-%d')
            end = datetime.strptime(end_date, '%Y-%m-%d')

            all_data = []
            current = start

            while current <= end:
                # 跳过周末
                if current.weekday() < 5:
                    date_str = current.strftime('%Y-%m-%d')
                    daily_data = self.load_daily_data(date_str)

                    if not daily_data.empty:
                        # 过滤指定股票
                        if symbols:
                            daily_data = daily_data[daily_data['symbol'].isin(symbols)]

                        if not daily_data.empty:
                            all_data.append(daily_data)

                current += timedelta(days=1)

            if all_data:
                combined_data = pd.concat(all_data, ignore_index=True)
                combined_data = combined_data.sort_values(['symbol', 'date'])

                logger.info(f"加载日期范围 {start_date} 到 {end_date} 数据成功: "
                          f"{len(combined_data)} 条记录, {combined_data['symbol'].nunique()} 只股票")
                return combined_data
            else:
                logger.warning(f"日期范围 {start_date} 到 {end_date} 没有数据")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"加载日期范围数据失败: {e}")
            return pd.DataFrame()

    def get_available_dates(self) -> List[str]:
        """获取可用的日期列表"""
        try:
            daily_data_path = Path(settings.data.raw_data_path) / "daily"

            if not daily_data_path.exists():
                return []

            dates = []

            # 遍历年份目录
            for year_dir in daily_data_path.iterdir():
                if not year_dir.is_dir() or not year_dir.name.isdigit():
                    continue

                # 遍历月份目录
                for month_dir in year_dir.iterdir():
                    if not month_dir.is_dir():
                        continue

                    # 遍历日期文件
                    for file_path in month_dir.glob("*.parquet"):
                        date_str = file_path.stem

                        try:
                            # 验证日期格式
                            datetime.strptime(date_str, '%Y-%m-%d')
                            dates.append(date_str)
                        except ValueError:
                            continue

            return sorted(dates)

        except Exception as e:
            logger.error(f"获取可用日期失败: {e}")
            return []

    def load_stock_data_from_daily_files(self, symbol: str, start_date: str = None,
                                        end_date: str = None) -> pd.DataFrame:
        """从按日期存储的文件中加载股票数据

        Args:
            symbol: 股票代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)

        Returns:
            股票数据
        """
        try:
            data_frames = []
            raw_path = Path(self.config.raw_data_path)

            if not raw_path.exists():
                logger.warning(f"数据路径不存在: {raw_path}")
                return pd.DataFrame()

            # 确定日期范围
            if start_date:
                start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            else:
                start_dt = datetime(2023, 1, 1)

            if end_date:
                end_dt = datetime.strptime(end_date, "%Y-%m-%d")
            else:
                end_dt = datetime.now()

            # 遍历年月目录
            for year_dir in raw_path.iterdir():
                if not year_dir.is_dir() or not year_dir.name.isdigit():
                    continue

                year = int(year_dir.name)
                if year < start_dt.year or year > end_dt.year:
                    continue

                for month_dir in year_dir.iterdir():
                    if not month_dir.is_dir():
                        continue

                    month = int(month_dir.name)

                    # 遍历日期文件
                    for file_path in month_dir.glob("*.parquet"):
                        date_str = file_path.stem  # 如20250721

                        try:
                            # 解析日期
                            if len(date_str) == 8:  # YYYYMMDD格式
                                file_date = datetime.strptime(date_str, "%Y%m%d")
                            else:
                                continue

                            # 检查日期范围
                            if file_date < start_dt or file_date > end_dt:
                                continue

                            # 读取文件
                            df = pd.read_parquet(file_path)
                            if df.empty or 'symbol' not in df.columns:
                                continue

                            # 过滤指定股票
                            symbol_data = df[df['symbol'] == symbol]
                            if not symbol_data.empty:
                                # 确保日期列格式正确
                                if 'date' in symbol_data.columns:
                                    symbol_data = symbol_data.copy()
                                    symbol_data['date'] = pd.to_datetime(symbol_data['date'])
                                data_frames.append(symbol_data)

                        except Exception as e:
                            logger.debug(f"读取文件 {file_path} 失败: {e}")
                            continue

            if not data_frames:
                logger.debug(f"股票 {symbol} 在指定日期范围内没有数据")
                return pd.DataFrame()

            # 合并数据
            combined_data = pd.concat(data_frames, ignore_index=True)
            combined_data = combined_data.sort_values('date').reset_index(drop=True)

            logger.debug(f"从日期文件加载股票 {symbol} 数据成功: {len(combined_data)} 条记录")
            return combined_data

        except Exception as e:
            logger.error(f"从日期文件加载股票 {symbol} 数据失败: {e}")
            return pd.DataFrame()

    def load_stock_data_from_daily(self, symbol: str, start_date: str = None,
                                  end_date: str = None) -> pd.DataFrame:
        """从按日期存储的文件中加载股票数据 (兼容性方法)

        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            股票数据
        """
        return self.load_stock_data_from_daily_files(symbol, start_date, end_date)
    
    def get_date_range(self, symbol: str) -> Tuple[Optional[str], Optional[str]]:
        """获取股票数据的日期范围
        
        Args:
            symbol: 股票代码
            
        Returns:
            (开始日期, 结束日期)
        """
        data = self.load_stock_data(symbol)
        
        if data.empty:
            return None, None
        
        start_date = data['date'].min().strftime("%Y-%m-%d")
        end_date = data['date'].max().strftime("%Y-%m-%d")
        
        return start_date, end_date
    
    def get_data_summary(self) -> Dict[str, any]:
        """获取数据概览"""
        symbols = self.get_available_symbols()
        
        summary = {
            'total_symbols': len(symbols),
            'symbols': symbols[:10],  # 只显示前10个
            'date_ranges': {}
        }
        
        # 获取几个样本股票的日期范围
        for symbol in symbols[:5]:
            start_date, end_date = self.get_date_range(symbol)
            if start_date and end_date:
                summary['date_ranges'][symbol] = {
                    'start': start_date,
                    'end': end_date
                }
        
        return summary
