"""
MiniQMT 数据采集器

基于 MiniQMT 采集股票历史数据，与 collector.py 接口保持一致
"""
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
from pathlib import Path
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed

import pandas as pd
from tqdm import tqdm

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 使用绝对导入
from app.strategy.config import settings
from app.strategy.data.validator import DataValidator
from app.strategy.data.transformer import DataTransformer

from app.utils.logger import get_logger
logger = get_logger(__name__)
from xtquant import xtdata

# xtdata.set_data_home_dir("D:/shared_miniqmt_data")  # 所有策略指向此目录

class MiniQMTCollector:
    """MiniQMT 数据采集器"""
    
    def __init__(self):
        self.validator = DataValidator()
        self.transformer = DataTransformer()
        self.config = settings.data
        self._context_info = None
        
    def _init_qmt_context(self):
        """初始化 QMT 上下文环境"""
        try:
            # 尝试导入 QMT 模块

            self._context_info = xtdata
            logger.info("QMT 上下文环境初始化成功")
            return True
        except ImportError as e:
            logger.error(f"QMT 环境未找到: {e}")
            logger.error("请确保在 QMT 环境中运行此脚本，或安装 xtquant 模块")
            raise ImportError("QMT 环境不可用，无法继续执行数据采集")
        except Exception as e:
            logger.error(f"QMT 环境初始化失败: {e}")
            raise RuntimeError(f"QMT 环境初始化失败: {e}")
    
    def get_stock_list(self) -> pd.DataFrame:
        """获取股票列表"""
        # 确保 QMT 环境已初始化
        self._init_qmt_context()

        try:
            # 使用QMT API获取股票列表
            stock_list = None

            # 首先尝试下载板块数据
            try:
                self._context_info.download_sector_data()
                logger.debug("板块数据下载完成")
            except Exception as e:
                logger.warning(f"下载板块数据失败: {e}")

            # 方法1: 使用get_stock_list_in_sector获取沪深A股
            try:
                stock_list = self._context_info.get_stock_list_in_sector('沪深A股')
                logger.debug(f"使用get_stock_list_in_sector获取到数据: {type(stock_list)}, 数量: {len(stock_list) if stock_list else 0}")
            except Exception as e:
                logger.warning(f"get_stock_list_in_sector('沪深A股')失败: {e}")

            # 方法2: 尝试其他板块名称
            if not stock_list:
                try:
                    # 获取所有板块列表
                    sectors = self._context_info.get_sector_list()
                    logger.debug(f"可用板块: {sectors[:10] if sectors else []}")  # 只显示前10个

                    # 尝试找到A股相关板块
                    for sector in sectors:
                        if 'A股' in sector or '全部' in sector or '沪深' in sector:
                            try:
                                stock_list = self._context_info.get_stock_list_in_sector(sector)
                                if stock_list:
                                    logger.debug(f"使用板块'{sector}'获取到{len(stock_list)}只股票")
                                    break
                            except Exception as e:
                                logger.debug(f"尝试板块'{sector}'失败: {e}")
                                continue
                except Exception as e:
                    logger.warning(f"获取板块列表失败: {e}")

            # 方法3: 使用预定义的股票列表进行测试
            if not stock_list:
                logger.warning("无法从QMT获取股票列表，使用预定义列表进行测试")
                stock_list = [
                    '600051.SH', '605090.SH', '000001.SZ', '000002.SZ',
                    '600000.SH', '600036.SH', '000858.SZ', '002415.SZ',
                    '600519.SH', '000858.SZ'
                ]

            # 检查返回的数据
            if stock_list is None:
                raise RuntimeError("无法获取股票列表")

            # 转换为标准格式
            if isinstance(stock_list, list):
                codes = []
                names = []
                for stock in stock_list:
                    if isinstance(stock, str):
                        # 提取股票代码
                        code = stock.split('.')[0] if '.' in stock else stock
                        codes.append(code)
                        # 获取股票名称
                        name = self._get_stock_name(stock)
                        names.append(name)

                stock_data = {'code': codes, 'name': names}
                result_df = pd.DataFrame(stock_data)
                logger.info(f"获取到 {len(result_df)} 只股票")
                return result_df

            elif isinstance(stock_list, pd.DataFrame):
                logger.info(f"获取到 {len(stock_list)} 只股票")
                return stock_list

            else:
                raise RuntimeError(f"不支持的股票列表格式: {type(stock_list)}")

        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            raise RuntimeError(f"获取股票列表失败: {e}")
    
    def _get_stock_name(self, stock_code: str) -> str:
        """获取股票名称"""
        try:
            if self._context_info and hasattr(self._context_info, 'get_instrument_detail'):
                detail = self._context_info.get_instrument_detail(stock_code)
                if detail and isinstance(detail, dict):
                    return detail.get('InstrumentName', stock_code)

            # 如果无法获取名称，返回代码本身
            return stock_code

        except Exception as e:
            logger.debug(f"获取股票 {stock_code} 名称失败: {e}")
            return stock_code
    
    def get_stock_daily_data(self, symbol: str, start_date: str, end_date: str,
                           validate_data: bool = True) -> pd.DataFrame:
        """获取股票日线数据

        Args:
            symbol: 股票代码
            start_date: 开始日期 YYYY-MM-DD
            end_date: 结束日期 YYYY-MM-DD
            validate_data: 是否进行数据验证，默认True

        Returns:
            股票日线数据DataFrame
        """
        # 确保 QMT 环境已初始化
        self._init_qmt_context()

        try:
            # 转换股票代码格式
            qmt_symbol = self._convert_symbol_format(symbol)
            logger.debug(f"转换后的股票代码: {qmt_symbol}")

            # 转换日期格式为QMT需要的格式
            try:
                start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                end_dt = datetime.strptime(end_date, "%Y-%m-%d")

                # QMT需要YYYYMMDD格式
                qmt_start_date = start_dt.strftime("%Y%m%d")
                qmt_end_date = end_dt.strftime("%Y%m%d")

                logger.debug(f"QMT日期格式: {qmt_start_date} 到 {qmt_end_date}")

            except ValueError as e:
                logger.error(f"日期格式错误: {e}")
                raise ValueError(f"日期格式错误: {start_date}, {end_date}")

            # 首先下载历史数据到本地缓存
            logger.info(f"开始下载股票 {qmt_symbol} 的历史数据...")
            try:
                self._context_info.download_history_data(
                    stock_code=qmt_symbol,
                    period='1d',
                    start_time=qmt_start_date,
                    end_time=qmt_end_date
                )
                logger.info(f"股票 {qmt_symbol} 历史数据下载完成")
            except Exception as download_error:
                logger.error(f"下载历史数据失败: {download_error}")
                # 继续尝试获取数据，可能本地已有缓存

            # 使用 get_market_data_ex 获取历史数据（推荐方式）
            try:
                subscribe = False  # 设置订阅参数，使gmd_ex仅返回本地数据
                data = self._context_info.get_market_data_ex(
                    field_list=[],  # 空列表表示获取所有字段
                    stock_list=[qmt_symbol],
                    period='1d',
                    start_time=qmt_start_date,
                    end_time=qmt_end_date,
                    count=-1,  # -1表示获取全部数据
                    dividend_type='front',  # 前复权
                    # subscribe=subscribe  # 仅获取历史数据，不订阅实时数据
                )

                logger.debug(f"QMT API返回数据类型: {type(data)}")
                if data:
                    logger.debug(f"QMT API返回数据字段: {list(data.keys())}")

            except Exception as api_error:
                logger.error(f"get_market_data_ex调用失败: {api_error}")
                # 尝试使用旧版API
                try:
                    data = self._context_info.get_market_data(
                        field_list=[],
                        stock_list=[qmt_symbol],
                        period='1d',
                        start_time=qmt_start_date,
                        end_time=qmt_end_date,
                        count=-1,
                        dividend_type='front',
                        fill_data=True
                    )
                    logger.debug("使用get_market_data获取数据成功")
                except Exception as fallback_error:
                    logger.error(f"get_market_data也失败: {fallback_error}")
                    raise RuntimeError(f"所有QMT API调用都失败: {api_error}, {fallback_error}")

            # 检查返回的数据
            if data is None:
                logger.warning(f"股票 {symbol} 在 {start_date} 到 {end_date} 期间无数据 (API返回None)")
                return pd.DataFrame()

            if not isinstance(data, dict):
                logger.error(f"QMT API返回数据格式错误，期望dict，实际: {type(data)}")
                return pd.DataFrame()

            if not data:
                logger.warning(f"股票 {symbol} 在 {start_date} 到 {end_date} 期间无数据 (返回空字典)")
                return pd.DataFrame()

            logger.debug(f"QMT返回数据字段: {list(data.keys())}")

            # 处理QMT返回的数据格式
            # get_market_data_ex返回: {stock_code: DataFrame} 格式
            # get_market_data返回: {field: DataFrame(index=stocks, columns=times)} 格式

            result_df = None

            # 方式1: 检查是否是 {stock_code: DataFrame} 格式
            if qmt_symbol in data:
                result_df = data[qmt_symbol].copy()
                logger.debug(f"使用股票代码 {qmt_symbol} 提取数据: {len(result_df)} 行")
                logger.debug(f"数据索引类型: {type(result_df.index)}")
                logger.debug(f"数据索引前几个值: {result_df.index[:5].tolist()}")

                # 将索引转换为date列
                result_df = result_df.reset_index()
                # 检查索引列的名称
                if 'index' in result_df.columns:
                    result_df = result_df.rename(columns={'index': 'date'})
                elif result_df.columns[0] not in ['open', 'high', 'low', 'close', 'volume']:
                    # 第一列可能是日期列
                    result_df = result_df.rename(columns={result_df.columns[0]: 'date'})
                else:
                    # 如果没有明确的日期列，创建一个
                    logger.warning("无法识别日期列，将使用行索引作为日期")
                    result_df['date'] = range(len(result_df))

            # 方式2: 检查是否是 {field: DataFrame} 格式
            elif any(isinstance(v, pd.DataFrame) for v in data.values()):
                # 获取第一个DataFrame来检查格式
                first_field = list(data.keys())[0]
                first_df = data[first_field]

                if first_df.empty:
                    logger.warning(f"股票 {symbol} 在 {start_date} 到 {end_date} 期间无数据 (字段数据为空)")
                    return pd.DataFrame()

                # 检查股票是否在数据中
                if qmt_symbol in first_df.index:
                    # 提取该股票的数据并转换格式
                    stock_data = {}
                    for field, field_df in data.items():
                        if isinstance(field_df, pd.DataFrame) and qmt_symbol in field_df.index:
                            stock_data[field] = field_df.loc[qmt_symbol]

                    # 转换为DataFrame
                    result_df = pd.DataFrame(stock_data)
                    result_df.index.name = 'date'
                    result_df = result_df.reset_index()
                    logger.debug(f"从字段格式转换数据: {len(result_df)} 行")
                else:
                    logger.warning(f"返回的数据中未找到股票 {qmt_symbol}, 可用股票: {list(first_df.index)}")
                    return pd.DataFrame()

            if result_df is None or result_df.empty:
                logger.warning(f"股票 {symbol} 在 {start_date} 到 {end_date} 期间无数据")
                return pd.DataFrame()

            logger.debug(f"原始数据: {len(result_df)} 行, 列: {list(result_df.columns)}")

            # 数据标准化
            result_df = self._standardize_qmt_data(result_df, symbol)

            # 数据验证（可选）
            if validate_data:
                if not self.validator.validate_daily_data(result_df):
                    logger.warning(f"股票 {symbol} 数据验证失败")
                    return pd.DataFrame()
                logger.info(f"成功获取股票 {symbol} 数据 {len(result_df)} 条（已验证）")
            else:
                logger.info(f"成功获取股票 {symbol} 数据 {len(result_df)} 条（跳过验证）")

            return result_df

        except Exception as e:
            logger.error(f"获取股票 {symbol} 数据失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            raise RuntimeError(f"获取股票 {symbol} 数据失败: {e}")
    
    def _convert_symbol_format(self, symbol: str) -> str:
        """转换股票代码格式为 QMT 格式"""
        if '.' in symbol:
            return symbol
        
        # 根据代码判断市场
        if symbol.startswith(('60', '68', '90')):
            return f"{symbol}.SH"  # 上海
        elif symbol.startswith(('00', '30', '20')):
            return f"{symbol}.SZ"  # 深圳
        elif symbol.startswith(('8', '4')):
            return f"{symbol}.BJ"  # 北京
        else:
            return f"{symbol}.SH"  # 默认上海
    
    def _standardize_qmt_data(self, data: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """标准化 QMT 数据格式"""
        if data.empty:
            return data

        logger.debug(f"原始数据列: {list(data.columns)}")
        logger.debug(f"原始数据前几行:\n{data.head()}")

        # 创建数据副本避免修改原数据
        standardized_data = data.copy()

        # QMT 数据列映射 - 更全面的映射
        qmt_column_mapping = {
            'time': 'date',
            'open': 'open',
            'high': 'high',
            'low': 'low',
            'close': 'close',
            'volume': 'volume',
            'amount': 'amount',
            'pctChg': 'pct_change',
            'turnoverRate': 'turnover',
            'preClose': 'pre_close',
            'suspendFlag': 'suspend_flag'
        }

        # 重命名存在的列
        existing_columns = {k: v for k, v in qmt_column_mapping.items() if k in standardized_data.columns}
        if existing_columns:
            standardized_data = standardized_data.rename(columns=existing_columns)
            logger.debug(f"重命名列: {existing_columns}")

        # 处理日期列 - 检查多种可能的日期列名
        date_columns = ['date', 'time', 'datetime', 'timestamp']
        date_column = None

        for col in date_columns:
            if col in standardized_data.columns:
                date_column = col
                break

        # 如果没有找到日期列，检查索引是否包含日期信息
        if date_column is None:
            # 检查索引是否是数字格式的日期（如20250728）
            if hasattr(standardized_data.index, 'dtype'):
                index_values = standardized_data.index
                logger.debug(f"索引类型: {type(index_values)}, 索引值示例: {index_values[:3].tolist()}")

                # 检查索引是否是日期格式
                if (hasattr(index_values, 'dtype') and
                    ('int' in str(index_values.dtype) or 'object' in str(index_values.dtype))):
                    try:
                        # 尝试将索引转换为日期
                        # 如果索引是20250728这样的格式
                        test_value = str(index_values[0])
                        if len(test_value) == 8 and test_value.isdigit():
                            # 将索引转换为date列
                            standardized_data = standardized_data.reset_index()
                            if 'index' in standardized_data.columns:
                                standardized_data = standardized_data.rename(columns={'index': 'date'})
                                date_column = 'date'
                                logger.debug("从数字索引创建date列")
                        else:
                            # 尝试其他格式
                            standardized_data = standardized_data.reset_index()
                            if 'index' in standardized_data.columns:
                                standardized_data = standardized_data.rename(columns={'index': 'date'})
                                date_column = 'date'
                                logger.debug("从索引创建date列")
                    except Exception as e:
                        logger.debug(f"索引转换失败: {e}")

                elif 'datetime' in str(standardized_data.index.dtype) or 'timestamp' in str(standardized_data.index.dtype):
                    # 将datetime索引转换为date列
                    standardized_data = standardized_data.reset_index()
                    if 'index' in standardized_data.columns:
                        standardized_data = standardized_data.rename(columns={'index': 'date'})
                        date_column = 'date'
                        logger.debug("从datetime索引创建date列")

        # 处理日期格式
        if date_column and date_column in standardized_data.columns:
            try:
                date_series = standardized_data[date_column]
                logger.debug(f"处理日期列 {date_column}, 类型: {date_series.dtype}, 示例值: {date_series.iloc[0] if len(date_series) > 0 else 'N/A'}")

                if date_series.dtype == 'int64':
                    # 检查是否是YYYYMMDD格式
                    test_value = date_series.iloc[0]
                    if 20000000 <= test_value <= 30000000:  # YYYYMMDD格式
                        # 保持YYYYMMDD格式，不转换为datetime
                        standardized_data['date'] = date_series.astype(str)
                    else:
                        # 时间戳转换为YYYYMMDD格式
                        dt_series = pd.to_datetime(date_series, unit='ms')
                        standardized_data['date'] = dt_series.dt.strftime('%Y%m%d')
                elif date_series.dtype == 'object':
                    # 字符串格式，尝试多种解析方式
                    test_value = str(date_series.iloc[0])
                    if len(test_value) == 8 and test_value.isdigit():
                        # 已经是YYYYMMDD格式，保持不变
                        standardized_data['date'] = date_series.astype(str)
                    else:
                        # 转换为YYYYMMDD格式
                        dt_series = pd.to_datetime(date_series)
                        standardized_data['date'] = dt_series.dt.strftime('%Y%m%d')
                else:
                    # 转换为YYYYMMDD格式
                    dt_series = pd.to_datetime(date_series)
                    standardized_data['date'] = dt_series.dt.strftime('%Y%m%d')

                # 如果原列名不是date，删除原列
                if date_column != 'date':
                    standardized_data = standardized_data.drop(columns=[date_column])

                logger.debug(f"日期列处理完成，类型: {standardized_data['date'].dtype}")
            except Exception as e:
                logger.error(f"日期列处理失败: {e}")
                # 如果日期处理失败，创建一个简单的日期序列
                logger.warning("创建默认日期序列")
                date_range = pd.date_range(start='2025-01-01', periods=len(standardized_data), freq='D')
                standardized_data['date'] = date_range.strftime('%Y%m%d')
        else:
            logger.warning(f"未找到日期列，可用列: {list(standardized_data.columns)}")
            # 创建一个默认的日期列
            logger.warning("创建默认日期序列")
            date_range = pd.date_range(start='2025-01-01', periods=len(standardized_data), freq='D')
            standardized_data['date'] = date_range.strftime('%Y%m%d')

        # 添加股票代码
        standardized_data['symbol'] = symbol

        logger.debug(f"标准化后列: {list(standardized_data.columns)}")

        # 使用 transformer 进行标准化
        try:
            standardized_data = self.transformer.standardize_daily_data(standardized_data, symbol)
            logger.debug(f"transformer处理后列: {list(standardized_data.columns)}")
        except Exception as e:
            logger.error(f"transformer处理失败: {e}")
            # 如果transformer失败，继续使用当前数据

        return standardized_data
    

    
    def save_daily_data_by_date(self, symbol: str, data: pd.DataFrame) -> None:
        """按日期保存股票数据，将同一天的所有股票数据合并到一个文件

        Args:
            symbol: 股票代码
            data: 股票数据
        """
        if data.empty:
            return

        logger.debug(f"保存数据，股票: {symbol}, 数据形状: {data.shape}")
        logger.debug(f"数据列: {list(data.columns)}")

        # 检查是否有date列
        if 'date' not in data.columns:
            logger.error(f"数据中缺少date列，可用列: {list(data.columns)}")
            raise KeyError("数据中缺少date列")

        # 创建数据副本避免修改原数据
        data_copy = data.copy()

        # 按日期分组保存
        for date_str in data_copy['date'].unique():
            date_data = data_copy[data_copy['date'] == date_str].copy()

            # 解析日期用于创建目录结构
            try:
                if len(str(date_str)) == 8:  # YYYYMMDD格式
                    year = str(date_str)[:4]
                    month = str(date_str)[4:6]
                    day = str(date_str)[6:8]
                else:
                    # 尝试解析其他格式
                    dt = pd.to_datetime(date_str)
                    year = str(dt.year)
                    month = f"{dt.month:02d}"
                    day = f"{dt.day:02d}"
            except Exception as e:
                logger.error(f"解析日期失败: {date_str}, 错误: {e}")
                continue

            # 创建目录结构: year/month/
            dir_path = Path(self.config.raw_data_path) / year / month
            dir_path.mkdir(parents=True, exist_ok=True)

            # 文件名: YYYYMMDD.parquet
            file_path = dir_path / f"{year}{month}{day}.parquet"

            try:
                # 如果文件已存在，读取并合并数据
                if file_path.exists():
                    existing_data = pd.read_parquet(file_path)
                    # 移除该股票的旧数据（如果存在）
                    existing_data = existing_data[existing_data['symbol'] != symbol]
                    # 合并新数据
                    combined_data = pd.concat([existing_data, date_data], ignore_index=True)
                else:
                    combined_data = date_data

                # 按股票代码排序
                combined_data = combined_data.sort_values('symbol')

                # 保存合并后的数据
                combined_data.to_parquet(file_path, index=False)
                logger.debug(f"保存数据到 {file_path}, 股票数量: {combined_data['symbol'].nunique()}, 总记录数: {len(combined_data)}")

            except Exception as e:
                logger.error(f"保存数据到 {file_path} 失败: {e}")
                raise

    def save_daily_data_by_month(self, symbol: str, data: pd.DataFrame) -> None:
        """兼容性方法，调用新的按日期保存方法"""
        self.save_daily_data_by_date(symbol, data)
    
    def collect_stock_data(self, symbol: str, start_date: str = None, end_date: str = None) -> bool:
        """采集单只股票数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期，默认为配置的开始日期
            end_date: 结束日期，默认为今天
            
        Returns:
            是否成功
        """
        if start_date is None:
            start_date = self.config.update_start_date
        if end_date is None:
            end_date = datetime.now().strftime("%Y-%m-%d")
        
        try:
            # 获取数据
            data = self.get_stock_daily_data(symbol, start_date, end_date)
            
            if data.empty:
                return False
            
            # 保存数据
            self.save_daily_data_by_month(symbol, data)
            
            return True
            
        except Exception as e:
            logger.error(f"采集股票 {symbol} 数据失败: {e}")
            return False

    def download_stocks_data(self, stock_list: List[str], start_date: str, end_date: str,
                           batch_size: int = 500) -> bool:
        """批量下载股票历史数据

        Args:
            stock_list: 股票代码列表
            start_date: 开始日期 YYYY-MM-DD
            end_date: 结束日期 YYYY-MM-DD
            batch_size: 批量下载大小，默认100只股票一批

        Returns:
            是否成功
        """
        # 确保 QMT 环境已初始化
        self._init_qmt_context()

        try:
            # 转换日期格式
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")
            qmt_start_date = start_dt.strftime("%Y%m%d")
            qmt_end_date = end_dt.strftime("%Y%m%d")

            # 转换股票代码格式
            qmt_stock_list = [self._convert_symbol_format(symbol) for symbol in stock_list]

            logger.info(f"开始批量下载 {len(qmt_stock_list)} 只股票的历史数据...")
            logger.info(f"日期范围: {qmt_start_date} 到 {qmt_end_date}")

            # 分批下载以避免单次请求过大
            success_count = 0
            total_batches = (len(qmt_stock_list) + batch_size - 1) // batch_size

            for i in range(0, len(qmt_stock_list), batch_size):
                batch_stocks = qmt_stock_list[i:i + batch_size]
                batch_num = i // batch_size + 1

                logger.info(f"下载第 {batch_num}/{total_batches} 批，股票数量: {len(batch_stocks)}")

                try:
                    # 使用批量下载API
                    def download_progress(data):
                        if data.get('stockcode'):
                            logger.debug(f"下载进度: {data.get('finished', 0)}/{data.get('total', 0)} - {data.get('stockcode', '')} - {data.get('message', '')}")

                    self._context_info.download_history_data2(
                        stock_list=batch_stocks,
                        period='1d',
                        start_time=qmt_start_date,
                        end_time=qmt_end_date,
                        callback=download_progress
                    )

                    success_count += len(batch_stocks)
                    logger.info(f"第 {batch_num} 批下载完成")

                    # 批次间稍作延迟
                    if i + batch_size < len(qmt_stock_list):
                        time.sleep(1)

                except Exception as batch_error:
                    logger.error(f"第 {batch_num} 批下载失败: {batch_error}")
                    # 继续下载下一批
                    continue

            logger.info(f"批量下载完成，成功下载 {success_count}/{len(qmt_stock_list)} 只股票")
            return success_count > 0

        except Exception as e:
            logger.error(f"批量下载失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False

    def batch_save_daily_data(self, all_data: Dict[str, pd.DataFrame]) -> None:
        """高效批量保存每日数据，将同一天的所有股票数据合并

        Args:
            all_data: {股票代码: DataFrame} 格式的数据字典
        """
        if not all_data:
            return

        logger.info(f"开始批量保存 {len(all_data)} 只股票的数据...")
        start_time = time.time()

        # 第一步：将所有数据合并为一个大DataFrame
        all_dataframes = []
        for symbol, data in all_data.items():
            if not data.empty:
                all_dataframes.append(data)

        if not all_dataframes:
            logger.warning("没有有效数据需要保存")
            return

        # 合并所有数据
        logger.info("合并所有股票数据...")
        combined_all_data = pd.concat(all_dataframes, ignore_index=True)
        logger.info(f"合并完成，总记录数: {len(combined_all_data)}")

        # 第二步：按日期分组
        logger.info("按日期分组数据...")
        date_groups = combined_all_data.groupby('date')

        # 第三步：批量创建目录结构
        logger.info("创建目录结构...")
        unique_dates = combined_all_data['date'].unique()
        dir_paths = {}

        for date_str in unique_dates:
            try:
                if len(str(date_str)) == 8:  # YYYYMMDD格式
                    year = str(date_str)[:4]
                    month = str(date_str)[4:6]
                    day = str(date_str)[6:8]
                else:
                    # 尝试解析其他格式
                    dt = pd.to_datetime(date_str)
                    year = str(dt.year)
                    month = f"{dt.month:02d}"
                    day = f"{dt.day:02d}"

                # 创建目录结构
                dir_path = Path(self.config.raw_data_path) / year / month
                dir_path.mkdir(parents=True, exist_ok=True)

                # 文件路径
                file_path = dir_path / f"{year}{month}{day}.parquet"
                dir_paths[date_str] = file_path

            except Exception as e:
                logger.error(f"处理日期 {date_str} 失败: {e}")
                continue

        # 第四步：多线程并行保存文件
        logger.info(f"开始多线程保存 {len(dir_paths)} 个日期文件...")
        saved_count = 0

        def save_single_date(date_str, file_path):
            """保存单个日期的数据"""
            try:
                # 获取该日期的数据
                date_data = date_groups.get_group(date_str)

                # 按股票代码排序
                date_data = date_data.sort_values('symbol')

                # 保存数据
                date_data.to_parquet(file_path, index=False)

                return {
                    'success': True,
                    'date': date_str,
                    'file_path': file_path,
                    'records': len(date_data),
                    'stocks': date_data['symbol'].nunique()
                }

            except Exception as e:
                return {
                    'success': False,
                    'date': date_str,
                    'file_path': file_path,
                    'error': str(e)
                }

        # 使用线程池并行保存
        max_workers = min(8, len(dir_paths))  # 最多8个线程
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有保存任务
            future_to_date = {
                executor.submit(save_single_date, date_str, file_path): date_str
                for date_str, file_path in dir_paths.items()
            }

            # 收集结果
            for future in as_completed(future_to_date):
                result = future.result()
                if result['success']:
                    saved_count += 1
                    if saved_count % 10 == 0:  # 每10个文件报告一次进度
                        logger.info(f"已保存 {saved_count}/{len(dir_paths)} 个文件")
                else:
                    logger.error(f"保存日期 {result['date']} 数据失败: {result['error']}")

        end_time = time.time()
        duration = end_time - start_time

        logger.info(f"批量保存完成！")
        logger.info(f"总耗时: {duration:.2f} 秒")
        logger.info(f"成功保存: {saved_count}/{len(dir_paths)} 个文件")
        logger.info(f"平均每个文件: {duration/saved_count:.3f} 秒" if saved_count > 0 else "无文件保存")

        # 统计信息
        total_stocks = len(all_data)
        total_records = len(combined_all_data)
        logger.info(f"数据统计: {total_stocks} 只股票, {total_records} 条记录")

    def get_batch_stock_data(self, symbols: List[str], start_date: str, end_date: str,
                           validate_data: bool = True) -> Dict[str, pd.DataFrame]:
        """批量获取多只股票的日线数据

        Args:
            symbols: 股票代码列表
            start_date: 开始日期 YYYY-MM-DD
            end_date: 结束日期 YYYY-MM-DD
            validate_data: 是否进行数据验证，默认True

        Returns:
            {股票代码: DataFrame} 格式的数据字典
        """
        # 确保 QMT 环境已初始化
        self._init_qmt_context()

        try:
            # 转换股票代码格式
            qmt_symbols = [self._convert_symbol_format(symbol) for symbol in symbols]
            logger.info(f"批量获取 {len(qmt_symbols)} 只股票数据")

            # 转换日期格式
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")
            qmt_start_date = start_dt.strftime("%Y%m%d")
            qmt_end_date = end_dt.strftime("%Y%m%d")

            logger.debug(f"QMT日期格式: {qmt_start_date} 到 {qmt_end_date}")

            # 批量获取数据
            try:
                data = self._context_info.get_market_data_ex(
                    field_list=[],  # 空列表表示获取所有字段
                    stock_list=qmt_symbols,
                    period='1d',
                    start_time=qmt_start_date,
                    end_time=qmt_end_date,
                    count=-1,  # -1表示获取全部数据
                    dividend_type='front',  # 前复权
                    # subscribe=False  # 仅获取历史数据，不订阅实时数据
                )

                logger.debug(f"批量获取数据成功，返回数据类型: {type(data)}")
                if data:
                    logger.debug(f"返回数据包含股票: {list(data.keys())}")

            except Exception as api_error:
                logger.error(f"批量获取数据失败: {api_error}")
                raise RuntimeError(f"批量获取数据失败: {api_error}")

            # 检查返回的数据
            if data is None or not isinstance(data, dict) or not data:
                logger.warning("批量获取数据为空")
                return {}

            # 处理每只股票的数据
            result_data = {}
            for i, qmt_symbol in enumerate(qmt_symbols):
                original_symbol = symbols[i]

                if qmt_symbol in data:
                    stock_df = data[qmt_symbol].copy()

                    if not stock_df.empty:
                        # 将索引转换为date列
                        stock_df = stock_df.reset_index()
                        if 'index' in stock_df.columns:
                            stock_df = stock_df.rename(columns={'index': 'date'})

                        # 数据标准化
                        standardized_df = self._standardize_qmt_data(stock_df, original_symbol)

                        # 数据验证（可选）
                        if validate_data:
                            if self.validator.validate_daily_data(standardized_df):
                                result_data[original_symbol] = standardized_df
                                logger.debug(f"股票 {original_symbol} 数据处理成功: {len(standardized_df)} 条")
                            else:
                                logger.warning(f"股票 {original_symbol} 数据验证失败")
                        else:
                            # 跳过验证，直接使用数据
                            result_data[original_symbol] = standardized_df
                            logger.debug(f"股票 {original_symbol} 数据处理成功（跳过验证）: {len(standardized_df)} 条")
                    else:
                        logger.warning(f"股票 {original_symbol} 数据为空")
                else:
                    logger.warning(f"返回数据中未找到股票 {qmt_symbol}")

            logger.info(f"批量处理完成，成功获取 {len(result_data)}/{len(symbols)} 只股票数据")
            return result_data

        except Exception as e:
            logger.error(f"批量获取股票数据失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            raise RuntimeError(f"批量获取股票数据失败: {e}")

    def collect_all_stocks_data(self, start_date: str = None, end_date: str = None,
                               max_stocks: int = None, batch_size: int = 50,
                               validate_data: bool = False) -> Dict[str, bool]:
        """采集所有股票数据（优化版本，使用批量操作）

        Args:
            start_date: 开始日期
            end_date: 结束日期
            max_stocks: 最大股票数量，用于测试
            batch_size: 批量处理大小，默认50只股票一批
            validate_data: 是否进行数据验证，默认False（不验证）

        Returns:
            采集结果字典 {股票代码: 是否成功}
        """
        # 获取股票列表
        stock_list = self.get_stock_list()

        if max_stocks:
            stock_list = stock_list.head(max_stocks)

        # 提取股票代码列表
        symbols = stock_list['code'].tolist()

        logger.info(f"开始采集 {len(symbols)} 只股票数据，批量大小: {batch_size}")

        # 批量下载历史数据
        # if start_date and end_date:
        #     logger.info("开始批量下载历史数据...")
        #     download_success = self.download_stocks_data(symbols, start_date, end_date)
        #     if not download_success:
        #         logger.warning("批量下载失败，但继续尝试获取数据")

        results = {}
        all_stock_data = {}  # 存储所有股票数据用于批量保存

        # 分批处理股票
        for i in range(0, len(symbols), batch_size):
            batch_symbols = symbols[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(symbols) + batch_size - 1) // batch_size

            logger.info(f"处理第 {batch_num}/{total_batches} 批，股票数量: {len(batch_symbols)}")

            try:
                # 批量获取数据
                batch_data = self.get_batch_stock_data(batch_symbols, start_date, end_date, validate_data)

                # 更新结果
                for symbol in batch_symbols:
                    if symbol in batch_data and not batch_data[symbol].empty:
                        all_stock_data[symbol] = batch_data[symbol]
                        results[symbol] = True
                        logger.debug(f"股票 {symbol} 数据获取成功: {len(batch_data[symbol])} 条")
                    else:
                        results[symbol] = False
                        logger.warning(f"股票 {symbol} 数据获取失败或为空")

                # 批次间稍作延迟
                if i + batch_size < len(symbols):
                    time.sleep(self.config.retry_delay)

            except Exception as e:
                logger.error(f"第 {batch_num} 批数据获取失败: {e}")
                # 标记该批次所有股票为失败
                for symbol in batch_symbols:
                    results[symbol] = False

        # 批量保存所有数据
        if all_stock_data:
            logger.info("开始批量保存数据...")
            self.batch_save_daily_data(all_stock_data)

        # 统计结果
        success_count = sum(results.values())
        total_count = len(results)

        logger.info(f"数据采集完成: 成功 {success_count}/{total_count} 只股票")

        return results

    def update_recent_data(self, days: int = 5) -> Dict[str, bool]:
        """更新最近几天的数据

        Args:
            days: 更新最近几天的数据

        Returns:
            更新结果
        """
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")

        logger.info(f"更新最近 {days} 天的数据: {start_date} 到 {end_date}")

        return self.collect_all_stocks_data(start_date, end_date)

    def get_existing_symbols(self) -> List[str]:
        """获取已存在的股票代码列表（适配按日期存储的结构）"""
        symbols = set()

        raw_path = Path(self.config.raw_data_path)
        if not raw_path.exists():
            return []

        # 遍历所有年月目录，读取日期文件
        for year_dir in raw_path.iterdir():
            if not year_dir.is_dir() or not year_dir.name.isdigit():
                continue

            for month_dir in year_dir.iterdir():
                if not month_dir.is_dir():
                    continue

                # 只读取最新的几个文件来获取股票代码（优化性能）
                files = sorted(month_dir.glob("*.parquet"), reverse=True)
                sample_files = files[:3]  # 每个月取最新3个文件

                for file_path in sample_files:
                    try:
                        df = pd.read_parquet(file_path)
                        if not df.empty and 'symbol' in df.columns:
                            symbols.update(df['symbol'].unique())
                    except Exception as e:
                        logger.debug(f"读取文件 {file_path} 失败: {e}")
                        continue

        return sorted(list(symbols))

    def smart_incremental_update(self, symbol: str) -> Dict[str, Any]:
        """智能增量更新

        检查数据缺口并智能填补
        """
        try:
            # 导入数据管理器来加载现有数据
            from app.strategy.data.manager import DataManager
            manager = DataManager()

            # 获取现有数据
            existing_data = manager.load_stock_data(symbol)

            if existing_data.empty:
                # 没有数据，进行全量采集
                end_date = datetime.now().strftime("%Y-%m-%d")
                start_date = "2023-01-01"

                success = self.collect_stock_data(symbol, start_date, end_date)

                return {
                    'symbol': symbol,
                    'type': 'full_collection',
                    'success': success,
                    'start_date': start_date,
                    'end_date': end_date
                }

            # 检查数据缺口
            latest_date = existing_data['date'].max()
            today = datetime.now().date()

            # 计算需要更新的日期范围
            if latest_date.date() < today:
                # 有数据缺口，需要更新
                start_date = (latest_date + timedelta(days=1)).strftime("%Y-%m-%d")
                end_date = today.strftime("%Y-%m-%d")

                success = self.collect_stock_data(symbol, start_date, end_date)

                return {
                    'symbol': symbol,
                    'type': 'incremental_update',
                    'success': success,
                    'start_date': start_date,
                    'end_date': end_date,
                    'gap_days': (today - latest_date.date()).days
                }
            else:
                # 数据已是最新
                return {
                    'symbol': symbol,
                    'type': 'up_to_date',
                    'success': True,
                    'latest_date': latest_date.strftime("%Y-%m-%d")
                }

        except Exception as e:
            return {
                'symbol': symbol,
                'type': 'error',
                'success': False,
                'error': str(e)
            }

    def batch_smart_update(self, symbols: List[str] = None, max_workers: int = 4) -> Dict[str, Any]:
        """批量智能更新"""
        from concurrent.futures import ThreadPoolExecutor, as_completed

        if symbols is None:
            symbols = self.get_existing_symbols()

            # 如果没有已有数据，获取股票列表
            if not symbols:
                try:
                    stock_list = self.get_stock_list()
                    symbols = stock_list['code'].tolist()[:100]  # 限制数量
                except Exception as e:
                    logger.error(f"获取股票列表失败: {e}")
                    return {'success': False, 'error': str(e)}

        logger.info(f"开始批量智能更新 {len(symbols)} 只股票")

        results = {
            'total': len(symbols),
            'full_collection': 0,
            'incremental_update': 0,
            'up_to_date': 0,
            'errors': 0,
            'details': []
        }

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {
                executor.submit(self.smart_incremental_update, symbol): symbol
                for symbol in symbols
            }

            for future in as_completed(futures):
                result = future.result()
                results['details'].append(result)

                update_type = result.get('type', 'error')
                if update_type in results:
                    results[update_type] += 1

                symbol = result['symbol']
                if result['success']:
                    logger.info(f"股票 {symbol} 更新成功: {update_type}")
                else:
                    logger.warning(f"股票 {symbol} 更新失败: {result.get('error', '未知错误')}")

        logger.info(f"批量更新完成: 全量{results['full_collection']} 增量{results['incremental_update']} 最新{results['up_to_date']} 错误{results['errors']}")

        return results


if __name__ == '__main__':
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    try:
        collector = MiniQMTCollector()
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")  # 减少测试天数

        logger.info(f"开始批量采集数据，时间范围: {start_date} 到 {end_date}")

        # 使用优化后的批量采集，设置较小的批量大小进行测试
        results = collector.collect_all_stocks_data(
            start_date=start_date,
            end_date=end_date,
            max_stocks=None,      # 测试20只股票
            batch_size=1000,      # 每批10只股票
            validate_data=False # 关闭数据验证，提高成功率
        )

        logger.info(f"数据采集完成，结果统计:")
        success_count = sum(results.values())
        total_count = len(results)
        logger.info(f"成功: {success_count}/{total_count} 只股票")

        # 显示成功和失败的股票
        success_stocks = [k for k, v in results.items() if v]
        failed_stocks = [k for k, v in results.items() if not v]

        if success_stocks:
            logger.info(f"成功股票: {success_stocks}")
        if failed_stocks:
            logger.warning(f"失败股票: {failed_stocks}")

    except ImportError as e:
        logger.error(f"导入错误: {e}")
        logger.error("请确保在 QMT 环境中运行此脚本")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        sys.exit(1)