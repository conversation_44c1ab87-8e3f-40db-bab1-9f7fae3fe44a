"""
数据采集器

基于akshare采集东方财富等数据源的股票数据
"""
import os
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Any
from pathlib import Path

# import akshare as ak
import pandas as pd
from tqdm import tqdm

from ..config import settings
from .validator import DataValidator
from .transformer import DataTransformer

logger = logging.getLogger(__name__)

class DataCollector:
    """数据采集器"""
    
    def __init__(self):
        self.validator = DataValidator()
        self.transformer = DataTransformer()
        self.config = settings.data
        
    def get_stock_list(self) -> pd.DataFrame:
        """获取股票列表"""
        try:
            # 获取A股股票列表
            stock_list = ak.stock_info_a_code_name()
            logger.info(f"获取到 {len(stock_list)} 只股票")
            return stock_list
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            raise
    
    def get_stock_daily_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取股票日线数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期 YYYY-MM-DD
            end_date: 结束日期 YYYY-MM-DD
            
        Returns:
            股票日线数据DataFrame
        """
        try:
            # 使用akshare获取股票历史数据
            data = ak.stock_zh_a_hist(
                symbol=symbol,
                period="daily",
                start_date=start_date.replace("-", ""),
                end_date=end_date.replace("-", ""),
                adjust="qfq"  # 前复权
            )
            
            if data.empty:
                logger.warning(f"股票 {symbol} 在 {start_date} 到 {end_date} 期间无数据")
                return pd.DataFrame()
            
            # 数据标准化
            data = self.transformer.standardize_daily_data(data, symbol)
            
            # 数据验证
            if not self.validator.validate_daily_data(data):
                logger.warning(f"股票 {symbol} 数据验证失败")
                return pd.DataFrame()
            
            logger.debug(f"获取股票 {symbol} 数据 {len(data)} 条")
            return data
            
        except Exception as e:
            logger.error(f"获取股票 {symbol} 数据失败: {e}")
            return pd.DataFrame()
    
    def save_daily_data_by_month(self, symbol: str, data: pd.DataFrame) -> None:
        """按年月保存日线数据到parquet文件
        
        Args:
            symbol: 股票代码
            data: 股票数据
        """
        if data.empty:
            return
        
        # 按年月分组
        data['year'] = data['date'].dt.year
        data['month'] = data['date'].dt.month
        
        for (year, month), group_data in data.groupby(['year', 'month']):
            # 创建目录
            dir_path = Path(self.config.raw_data_path) / str(year) / f"{month:02d}"
            dir_path.mkdir(parents=True, exist_ok=True)
            
            # 保存文件
            file_path = dir_path / f"{symbol}.parquet"
            
            # 移除分组列
            save_data = group_data.drop(['year', 'month'], axis=1)
            
            try:
                save_data.to_parquet(file_path, index=False)
                logger.debug(f"保存数据到 {file_path}")
            except Exception as e:
                logger.error(f"保存数据到 {file_path} 失败: {e}")
    
    def collect_stock_data(self, symbol: str, start_date: str = None, end_date: str = None) -> bool:
        """采集单只股票数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期，默认为配置的开始日期
            end_date: 结束日期，默认为今天
            
        Returns:
            是否成功
        """
        if start_date is None:
            start_date = self.config.update_start_date
        if end_date is None:
            end_date = datetime.now().strftime("%Y-%m-%d")
        
        try:
            # 获取数据
            data = self.get_stock_daily_data(symbol, start_date, end_date)
            
            if data.empty:
                return False
            
            # 保存数据
            self.save_daily_data_by_month(symbol, data)
            
            return True
            
        except Exception as e:
            logger.error(f"采集股票 {symbol} 数据失败: {e}")
            return False
    
    def collect_all_stocks_data(self, start_date: str = None, end_date: str = None, 
                               max_stocks: int = None) -> Dict[str, bool]:
        """采集所有股票数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期  
            max_stocks: 最大股票数量，用于测试
            
        Returns:
            采集结果字典 {股票代码: 是否成功}
        """
        # 获取股票列表
        stock_list = self.get_stock_list()
        
        if max_stocks:
            stock_list = stock_list.head(max_stocks)
        
        results = {}
        
        # 使用进度条显示采集进度
        for _, stock in tqdm(stock_list.iterrows(), total=len(stock_list), desc="采集股票数据"):
            symbol = stock['code']
            
            # 采集数据
            success = self.collect_stock_data(symbol, start_date, end_date)
            results[symbol] = success
            
            # 避免请求过于频繁
            time.sleep(self.config.retry_delay)
        
        # 统计结果
        success_count = sum(results.values())
        total_count = len(results)
        
        logger.info(f"数据采集完成: 成功 {success_count}/{total_count} 只股票")
        
        return results
    
    def update_recent_data(self, days: int = 5) -> Dict[str, bool]:
        """更新最近几天的数据
        
        Args:
            days: 更新最近几天的数据
            
        Returns:
            更新结果
        """
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")
        
        logger.info(f"更新最近 {days} 天的数据: {start_date} 到 {end_date}")
        
        return self.collect_all_stocks_data(start_date, end_date)
    
    def get_existing_symbols(self) -> List[str]:
        """获取已存在的股票代码列表"""
        symbols = set()
        
        raw_path = Path(self.config.raw_data_path)
        if not raw_path.exists():
            return []
        
        # 遍历所有年月目录
        for year_dir in raw_path.iterdir():
            if not year_dir.is_dir():
                continue
            for month_dir in year_dir.iterdir():
                if not month_dir.is_dir():
                    continue
                for file_path in month_dir.glob("*.parquet"):
                    symbol = file_path.stem
                    symbols.add(symbol)
        
        return sorted(list(symbols))

    def smart_incremental_update(self, symbol: str) -> Dict[str, Any]:
        """智能增量更新

        检查数据缺口并智能填补
        """
        try:
            # 获取现有数据
            existing_data = self.manager.load_stock_data(symbol)

            if existing_data.empty:
                # 没有数据，进行全量采集
                end_date = datetime.now().strftime("%Y-%m-%d")
                start_date = "2023-01-01"

                success = self.collect_stock_data(symbol, start_date, end_date)

                return {
                    'symbol': symbol,
                    'type': 'full_collection',
                    'success': success,
                    'start_date': start_date,
                    'end_date': end_date
                }

            # 检查数据缺口
            latest_date = existing_data['date'].max()
            today = datetime.now().date()

            # 计算需要更新的日期范围
            if latest_date.date() < today:
                # 有数据缺口，需要更新
                start_date = (latest_date + timedelta(days=1)).strftime("%Y-%m-%d")
                end_date = today.strftime("%Y-%m-%d")

                success = self.collect_stock_data(symbol, start_date, end_date)

                return {
                    'symbol': symbol,
                    'type': 'incremental_update',
                    'success': success,
                    'start_date': start_date,
                    'end_date': end_date,
                    'gap_days': (today - latest_date.date()).days
                }
            else:
                # 数据已是最新
                return {
                    'symbol': symbol,
                    'type': 'up_to_date',
                    'success': True,
                    'latest_date': latest_date.strftime("%Y-%m-%d")
                }

        except Exception as e:
            return {
                'symbol': symbol,
                'type': 'error',
                'success': False,
                'error': str(e)
            }

    def batch_smart_update(self, symbols: List[str] = None, max_workers: int = 4) -> Dict[str, Any]:
        """批量智能更新"""
        from concurrent.futures import ThreadPoolExecutor, as_completed

        if symbols is None:
            symbols = self.manager.get_available_symbols()

            # 如果没有已有数据，获取股票列表
            if not symbols:
                try:
                    stock_list = self.get_stock_list()
                    symbols = stock_list['code'].tolist()[:100]  # 限制数量
                except Exception as e:
                    logger.error(f"获取股票列表失败: {e}")
                    return {'success': False, 'error': str(e)}

        logger.info(f"开始批量智能更新 {len(symbols)} 只股票")

        results = {
            'total': len(symbols),
            'full_collection': 0,
            'incremental_update': 0,
            'up_to_date': 0,
            'errors': 0,
            'details': []
        }

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {
                executor.submit(self.smart_incremental_update, symbol): symbol
                for symbol in symbols
            }

            for future in as_completed(futures):
                result = future.result()
                results['details'].append(result)

                update_type = result.get('type', 'error')
                if update_type in results:
                    results[update_type] += 1

                symbol = result['symbol']
                if result['success']:
                    logger.info(f"股票 {symbol} 更新成功: {update_type}")
                else:
                    logger.warning(f"股票 {symbol} 更新失败: {result.get('error', '未知错误')}")

        logger.info(f"批量更新完成: 全量{results['full_collection']} 增量{results['incremental_update']} 最新{results['up_to_date']} 错误{results['errors']}")

        return results
