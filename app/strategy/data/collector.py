"""
数据采集器

基于MiniQMT采集股票数据，支持批量采集和按日期存储
"""
import os
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Any
from pathlib import Path

import pandas as pd
from tqdm import tqdm

from ..config import settings
from .validator import DataValidator
from .transformer import DataTransformer
from .miniqmt_collector import MiniQMTCollector

logger = logging.getLogger(__name__)

class DataCollector:
    """数据采集器 - 基于MiniQMT的统一接口"""

    def __init__(self):
        self.validator = DataValidator()
        self.transformer = DataTransformer()
        self.config = settings.data
        # 使用MiniQMT作为底层数据源
        self._qmt_collector = MiniQMTCollector()
        
    def get_stock_list(self) -> pd.DataFrame:
        """获取股票列表"""
        try:
            # 使用MiniQMT获取A股股票列表
            stock_list = self._qmt_collector.get_stock_list()
            logger.info(f"获取到 {len(stock_list)} 只股票")
            return stock_list
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            raise
    
    def get_stock_daily_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取股票日线数据

        Args:
            symbol: 股票代码
            start_date: 开始日期 YYYY-MM-DD
            end_date: 结束日期 YYYY-MM-DD

        Returns:
            股票日线数据DataFrame
        """
        try:
            # 使用MiniQMT获取股票历史数据
            data = self._qmt_collector.get_stock_daily_data(symbol, start_date, end_date)

            if data.empty:
                logger.warning(f"股票 {symbol} 在 {start_date} 到 {end_date} 期间无数据")
                return pd.DataFrame()

            logger.debug(f"获取股票 {symbol} 数据 {len(data)} 条")
            return data

        except Exception as e:
            logger.error(f"获取股票 {symbol} 数据失败: {e}")
            return pd.DataFrame()
    
    def save_daily_data_by_month(self, symbol: str, data: pd.DataFrame) -> None:
        """按日期保存日线数据到parquet文件（兼容性方法）

        Args:
            symbol: 股票代码
            data: 股票数据
        """
        if data.empty:
            return

        # 使用MiniQMT的按日期保存方法
        self._qmt_collector.save_daily_data_by_date(symbol, data)

    def collect_stock_data(self, symbol: str, start_date: str = None, end_date: str = None) -> bool:
        """采集单只股票数据

        Args:
            symbol: 股票代码
            start_date: 开始日期，默认为配置的开始日期
            end_date: 结束日期，默认为今天

        Returns:
            是否成功
        """
        # 直接使用MiniQMT的采集方法
        return self._qmt_collector.collect_stock_data(symbol, start_date, end_date)
    
    def collect_all_stocks_data(self, start_date: str = None, end_date: str = None,
                               max_stocks: int = None, batch_size: int = 50,
                               validate_data: bool = False) -> Dict[str, bool]:
        """采集所有股票数据（使用MiniQMT批量优化）

        Args:
            start_date: 开始日期
            end_date: 结束日期
            max_stocks: 最大股票数量，用于测试
            batch_size: 批量处理大小，默认50只股票一批
            validate_data: 是否进行数据验证，默认False

        Returns:
            采集结果字典 {股票代码: 是否成功}
        """
        # 使用MiniQMT的批量采集方法
        return self._qmt_collector.collect_all_stocks_data(
            start_date=start_date,
            end_date=end_date,
            max_stocks=max_stocks,
            batch_size=batch_size,
            validate_data=validate_data
        )
    
    def update_recent_data(self, days: int = 5) -> Dict[str, bool]:
        """更新最近几天的数据

        Args:
            days: 更新最近几天的数据

        Returns:
            更新结果
        """
        # 使用MiniQMT的更新方法
        return self._qmt_collector.update_recent_data(days)
    
    def get_existing_symbols(self) -> List[str]:
        """获取已存在的股票代码列表"""
        # 使用MiniQMT的方法，但需要适配按日期存储的结构
        return self._qmt_collector.get_existing_symbols()

    def smart_incremental_update(self, symbol: str) -> Dict[str, Any]:
        """智能增量更新

        检查数据缺口并智能填补
        """
        # 使用MiniQMT的智能更新方法
        return self._qmt_collector.smart_incremental_update(symbol)

    def batch_smart_update(self, symbols: List[str] = None, max_workers: int = 4) -> Dict[str, Any]:
        """批量智能更新"""
        # 使用MiniQMT的批量智能更新方法
        return self._qmt_collector.batch_smart_update(symbols, max_workers)


    # 新增批量方法，利用MiniQMT的优势
    def get_batch_stock_data(self, symbols: List[str], start_date: str, end_date: str,
                           validate_data: bool = True) -> Dict[str, pd.DataFrame]:
        """批量获取多只股票的日线数据

        Args:
            symbols: 股票代码列表
            start_date: 开始日期 YYYY-MM-DD
            end_date: 结束日期 YYYY-MM-DD
            validate_data: 是否进行数据验证，默认True

        Returns:
            {股票代码: DataFrame} 格式的数据字典
        """
        return self._qmt_collector.get_batch_stock_data(symbols, start_date, end_date, validate_data)

    def batch_save_daily_data(self, all_data: Dict[str, pd.DataFrame]) -> None:
        """高效批量保存每日数据

        Args:
            all_data: {股票代码: DataFrame} 格式的数据字典
        """
        self._qmt_collector.batch_save_daily_data(all_data)

    def download_stocks_data(self, stock_list: List[str], start_date: str, end_date: str,
                           batch_size: int = 500) -> bool:
        """批量下载股票历史数据

        Args:
            stock_list: 股票代码列表
            start_date: 开始日期 YYYY-MM-DD
            end_date: 结束日期 YYYY-MM-DD
            batch_size: 批量下载大小，默认500只股票一批

        Returns:
            是否成功
        """
        return self._qmt_collector.download_stocks_data(stock_list, start_date, end_date, batch_size)
