"""
全量股票数据采集脚本

支持全量采集和增量更新
"""
import os
import sys
import time
import json
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Dict, Set, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from app.strategy.data.collector import DataCollector
from app.strategy.data.manager import DataManager
from app.strategy.config import settings
import pandas as pd

class FullDataCollector:
    """全量数据采集器"""
    
    def __init__(self):
        self.collector = DataCollector()
        self.manager = DataManager()
        self.progress_file = Path(settings.data.processed_data_path) / "collection_progress.json"
        self.lock = threading.Lock()
        
        # 确保目录存在
        os.makedirs(os.path.dirname(self.progress_file), exist_ok=True)
    
    def get_all_stock_symbols(self) -> List[str]:
        """获取所有股票代码"""
        print("📋 获取股票列表...")
        try:
            stock_list = self.collector.get_stock_list()
            symbols = stock_list['code'].tolist()
            print(f"✓ 获取到 {len(symbols)} 只股票")
            return symbols
        except Exception as e:
            print(f"❌ 获取股票列表失败: {e}")
            return []
    
    def get_existing_symbols(self) -> Dict[str, Dict]:
        """获取已存在的股票数据信息"""
        print("🔍 检查已存在的数据...")
        existing_data = {}
        all_symbols = set()

        raw_path = Path(settings.data.raw_data_path)
        if not raw_path.exists():
            return existing_data

        # 先快速扫描获取所有股票代码（只读取最新的几个文件）
        print("📋 快速扫描股票代码...")
        sample_files = []

        # 遍历所有年月目录，收集最新的文件
        for year_dir in sorted(raw_path.iterdir(), reverse=True):
            if not year_dir.is_dir() or not year_dir.name.isdigit():
                continue

            for month_dir in sorted(year_dir.iterdir(), reverse=True):
                if not month_dir.is_dir():
                    continue

                # 获取该月的最新几个文件
                files = sorted(month_dir.glob("*.parquet"), reverse=True)
                sample_files.extend(files[:3])  # 每个月取最新3个文件

                if len(sample_files) >= 10:  # 总共取10个样本文件
                    break

            if len(sample_files) >= 10:
                break

        # 从样本文件中获取股票代码
        for file_path in sample_files:
            try:
                df = pd.read_parquet(file_path)
                if not df.empty and 'symbol' in df.columns:
                    all_symbols.update(df['symbol'].unique())
            except Exception as e:
                print(f"⚠ 读取样本文件 {file_path} 失败: {e}")
                continue

        print(f"📊 发现 {len(all_symbols)} 只股票代码")

        # 为每只股票初始化数据结构
        for symbol in all_symbols:
            existing_data[symbol] = {
                'files': [],
                'date_range': {'start': None, 'end': None},
                'total_records': 0
            }

        # 快速统计：只检查部分股票的详细信息
        sample_symbols = list(all_symbols)[:50]  # 只检查前50只股票的详细信息
        print(f"📈 详细检查前 {len(sample_symbols)} 只股票的数据范围...")

        for symbol in sample_symbols:
            try:
                # 快速检查：只加载最近30天的数据
                end_date = datetime.now().strftime("%Y-%m-%d")
                start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")

                data = self.manager.load_stock_data_from_daily_files(symbol, start_date, end_date)
                if not data.empty:
                    existing_data[symbol]['date_range']['start'] = data['date'].min().strftime('%Y-%m-%d')
                    existing_data[symbol]['date_range']['end'] = data['date'].max().strftime('%Y-%m-%d')
                    existing_data[symbol]['total_records'] = len(data)
                else:
                    # 如果最近30天没有数据，设置默认值
                    existing_data[symbol]['total_records'] = 0
            except Exception as e:
                print(f"⚠ 检查股票 {symbol} 数据失败: {e}")
                existing_data[symbol]['total_records'] = 0

        # 对于其他股票，设置默认值
        for symbol in all_symbols:
            if symbol not in sample_symbols:
                existing_data[symbol]['total_records'] = 100  # 假设有数据

        print(f"✓ 发现 {len(existing_data)} 只股票已有数据")
        return existing_data
    
    def save_progress(self, progress: Dict):
        """保存采集进度"""
        with self.lock:
            try:
                with open(self.progress_file, 'w', encoding='utf-8') as f:
                    json.dump(progress, f, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"⚠ 保存进度失败: {e}")
    
    def load_progress(self) -> Dict:
        """加载采集进度"""
        if not self.progress_file.exists():
            return {}
        
        try:
            with open(self.progress_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠ 加载进度失败: {e}")
            return {}
    
    def collect_single_stock(self, symbol: str, start_date: str, end_date: str, 
                           is_incremental: bool = False) -> Dict:
        """采集单只股票数据"""
        try:
            success = self.collector.collect_stock_data(symbol, start_date, end_date)
            
            result = {
                'symbol': symbol,
                'success': success,
                'start_date': start_date,
                'end_date': end_date,
                'is_incremental': is_incremental,
                'timestamp': datetime.now().isoformat()
            }
            
            if success:
                # 验证数据
                data = self.manager.load_stock_data(symbol, start_date, end_date)
                result['records'] = len(data) if not data.empty else 0
            else:
                result['records'] = 0
                result['error'] = '数据采集失败'
            
            return result
            
        except Exception as e:
            return {
                'symbol': symbol,
                'success': False,
                'error': str(e),
                'start_date': start_date,
                'end_date': end_date,
                'is_incremental': is_incremental,
                'timestamp': datetime.now().isoformat()
            }
    
    def full_collection(self, start_date: str = "2025-01-01",
                       max_workers: int = 4, batch_size: int = 50) -> Dict:
        """全量数据采集"""
        print("🚀 开始全量数据采集...")
        print(f"📅 采集时间范围: {start_date} 到 {datetime.now().strftime('%Y-%m-%d')}")
        print(f"🔧 并发数: {max_workers}, 批次大小: {batch_size}")
        
        # 获取所有股票
        all_symbols = self.get_all_stock_symbols()
        if not all_symbols:
            return {'success': False, 'error': '无法获取股票列表'}
        
        # 检查已存在的数据
        existing_data = self.get_existing_symbols()
        
        # 确定需要采集的股票
        symbols_to_collect = []
        for symbol in all_symbols:
            if symbol not in existing_data:
                symbols_to_collect.append(symbol)
            else:
                # 检查数据是否完整
                info = existing_data[symbol]
                if (not info['date_range']['start'] or 
                    info['date_range']['start'] > start_date or
                    info['total_records'] < 100):  # 数据太少，重新采集
                    symbols_to_collect.append(symbol)
        
        print(f"📊 需要采集: {len(symbols_to_collect)} 只股票")
        print(f"📊 已有数据: {len(existing_data)} 只股票")
        
        if not symbols_to_collect:
            print("✅ 所有股票数据已存在，无需采集")
            return {'success': True, 'message': '数据已完整'}
        
        # 加载进度
        progress = self.load_progress()
        if 'full_collection' not in progress:
            progress['full_collection'] = {
                'start_time': datetime.now().isoformat(),
                'total_symbols': len(symbols_to_collect),
                'completed': [],
                'failed': [],
                'current_batch': 0
            }
        
        completed_symbols = set(progress['full_collection']['completed'])
        failed_symbols = set(progress['full_collection']['failed'])
        remaining_symbols = [s for s in symbols_to_collect 
                           if s not in completed_symbols and s not in failed_symbols]
        
        print(f"📈 进度: 已完成 {len(completed_symbols)}, 失败 {len(failed_symbols)}, 剩余 {len(remaining_symbols)}")
        
        end_date = datetime.now().strftime("%Y-%m-%d")
        
        # 分批处理
        total_batches = (len(remaining_symbols) + batch_size - 1) // batch_size
        
        for batch_idx in range(0, len(remaining_symbols), batch_size):
            batch_symbols = remaining_symbols[batch_idx:batch_idx + batch_size]
            current_batch = batch_idx // batch_size + 1
            
            print(f"\n📦 处理批次 {current_batch}/{total_batches} ({len(batch_symbols)} 只股票)")
            
            # 并发采集
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = {
                    executor.submit(self.collect_single_stock, symbol, start_date, end_date): symbol
                    for symbol in batch_symbols
                }
                
                batch_results = []
                for future in as_completed(futures):
                    result = future.result()
                    batch_results.append(result)
                    
                    symbol = result['symbol']
                    if result['success']:
                        progress['full_collection']['completed'].append(symbol)
                        print(f"  ✓ {symbol}: {result.get('records', 0)} 条记录")
                    else:
                        progress['full_collection']['failed'].append(symbol)
                        print(f"  ✗ {symbol}: {result.get('error', '未知错误')}")
                    
                    # 保存进度
                    progress['full_collection']['current_batch'] = current_batch
                    self.save_progress(progress)
            
            # 批次间休息
            if current_batch < total_batches:
                print(f"⏸ 批次完成，休息 5 秒...")
                time.sleep(5)
        
        # 完成统计
        total_completed = len(progress['full_collection']['completed'])
        total_failed = len(progress['full_collection']['failed'])
        
        progress['full_collection']['end_time'] = datetime.now().isoformat()
        progress['full_collection']['final_stats'] = {
            'total_symbols': len(symbols_to_collect),
            'completed': total_completed,
            'failed': total_failed,
            'success_rate': total_completed / len(symbols_to_collect) if symbols_to_collect else 0
        }
        
        self.save_progress(progress)
        
        print(f"\n🎉 全量采集完成!")
        print(f"📊 总计: {len(symbols_to_collect)} 只股票")
        print(f"✅ 成功: {total_completed} 只")
        print(f"❌ 失败: {total_failed} 只")
        print(f"📈 成功率: {total_completed/len(symbols_to_collect)*100:.1f}%")
        
        return {
            'success': True,
            'stats': progress['full_collection']['final_stats']
        }
    
    def incremental_update(self, days: int = 5, max_workers: int = 4) -> Dict:
        """增量数据更新"""
        print(f"🔄 开始增量数据更新 (最近 {days} 天)")
        
        # 获取已有数据的股票
        existing_data = self.get_existing_symbols()
        
        if not existing_data:
            print("❌ 没有已存在的数据，请先进行全量采集")
            return {'success': False, 'error': '没有已存在的数据'}
        
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")
        
        print(f"📅 更新时间范围: {start_date} 到 {end_date}")
        print(f"📊 需要更新: {len(existing_data)} 只股票")
        
        # 加载进度
        progress = self.load_progress()
        update_key = f'incremental_update_{datetime.now().strftime("%Y%m%d")}'
        
        if update_key not in progress:
            progress[update_key] = {
                'start_time': datetime.now().isoformat(),
                'total_symbols': len(existing_data),
                'completed': [],
                'failed': []
            }
        
        completed_symbols = set(progress[update_key]['completed'])
        failed_symbols = set(progress[update_key]['failed'])
        remaining_symbols = [s for s in existing_data.keys() 
                           if s not in completed_symbols and s not in failed_symbols]
        
        print(f"📈 进度: 已完成 {len(completed_symbols)}, 失败 {len(failed_symbols)}, 剩余 {len(remaining_symbols)}")
        
        # 并发更新
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {
                executor.submit(self.collect_single_stock, symbol, start_date, end_date, True): symbol
                for symbol in remaining_symbols
            }
            
            for i, future in enumerate(as_completed(futures), 1):
                result = future.result()
                symbol = result['symbol']
                
                if result['success']:
                    progress[update_key]['completed'].append(symbol)
                    print(f"  ✓ [{i}/{len(remaining_symbols)}] {symbol}: {result.get('records', 0)} 条记录")
                else:
                    progress[update_key]['failed'].append(symbol)
                    print(f"  ✗ [{i}/{len(remaining_symbols)}] {symbol}: {result.get('error', '未知错误')}")
                
                # 定期保存进度
                if i % 10 == 0:
                    self.save_progress(progress)
                
                # 避免请求过于频繁
                time.sleep(0.5)
        
        # 完成统计
        total_completed = len(progress[update_key]['completed'])
        total_failed = len(progress[update_key]['failed'])
        
        progress[update_key]['end_time'] = datetime.now().isoformat()
        progress[update_key]['final_stats'] = {
            'total_symbols': len(existing_data),
            'completed': total_completed,
            'failed': total_failed,
            'success_rate': total_completed / len(existing_data) if existing_data else 0
        }
        
        self.save_progress(progress)
        
        print(f"\n🎉 增量更新完成!")
        print(f"📊 总计: {len(existing_data)} 只股票")
        print(f"✅ 成功: {total_completed} 只")
        print(f"❌ 失败: {total_failed} 只")
        print(f"📈 成功率: {total_completed/len(existing_data)*100:.1f}%")
        
        return {
            'success': True,
            'stats': progress[update_key]['final_stats']
        }
    
    def show_data_summary(self):
        """显示数据概览"""
        print("\n📊 数据概览:")
        
        existing_data = self.get_existing_symbols()
        
        if not existing_data:
            print("  ❌ 没有数据")
            return
        
        total_records = sum(info['total_records'] for info in existing_data.values())
        total_files = sum(len(info['files']) for info in existing_data.values())
        
        print(f"  📈 股票数量: {len(existing_data)}")
        print(f"  📄 文件数量: {total_files}")
        print(f"  📊 总记录数: {total_records:,}")
        
        # 数据完整性统计
        complete_data = 0
        incomplete_data = 0
        
        for symbol, info in existing_data.items():
            if info['total_records'] >= 200:  # 认为200条以上是完整数据
                complete_data += 1
            else:
                incomplete_data += 1
        
        print(f"  ✅ 完整数据: {complete_data} 只")
        print(f"  ⚠ 不完整数据: {incomplete_data} 只")
        
        # 最新数据统计
        today = datetime.now().strftime('%Y-%m-%d')
        recent_data = 0
        
        for symbol, info in existing_data.items():
            if info['date_range']['end'] and info['date_range']['end'] >= (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d'):
                recent_data += 1
        
        print(f"  🕐 最近一周有数据: {recent_data} 只")

def main():
    """主函数"""
    print("🚀 全量股票数据采集系统")
    
    collector = FullDataCollector()
    
    print("\n选择操作:")
    print("1. 全量数据采集")
    print("2. 增量数据更新")
    print("3. 查看数据概览")
    print("4. 查看采集进度")
    
    choice = input("\n请选择 (1/2/3/4): ").strip()
    
    if choice == "1":
        print("\n⚠️ 全量采集将花费较长时间，建议在网络稳定时进行")
        
        start_date = input("请输入开始日期 (YYYY-MM-DD，默认2023-01-01): ").strip()
        if not start_date:
            start_date = "2023-01-01"
        
        max_workers = input("请输入并发数 (默认4): ").strip()
        max_workers = int(max_workers) if max_workers.isdigit() else 4
        
        batch_size = input("请输入批次大小 (默认50): ").strip()
        batch_size = int(batch_size) if batch_size.isdigit() else 50
        
        confirm = input(f"\n确认开始全量采集？(y/n): ").lower().strip()
        if confirm == 'y':
            collector.full_collection(start_date, max_workers, batch_size)
        else:
            print("已取消")
    
    elif choice == "2":
        days = input("请输入更新天数 (默认5): ").strip()
        days = int(days) if days.isdigit() else 5
        
        max_workers = input("请输入并发数 (默认4): ").strip()
        max_workers = int(max_workers) if max_workers.isdigit() else 4
        
        collector.incremental_update(days, max_workers)
    
    elif choice == "3":
        collector.show_data_summary()
    
    elif choice == "4":
        progress = collector.load_progress()
        if progress:
            print("\n📋 采集进度:")
            for key, value in progress.items():
                print(f"  {key}: {value}")
        else:
            print("\n📋 没有采集进度记录")
    
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
